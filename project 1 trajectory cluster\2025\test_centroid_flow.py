"""
测试修改后的中心点计算流程
验证：噪点筛选 → 中心点计算 的新流程
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_new_centroid_flow():
    """测试新的中心点计算流程"""
    
    print("=" * 60)
    print("🧪 测试新的中心点计算流程")
    print("   流程：数据切分 → 聚类 → 噪点筛选 → 中心点计算")
    print("=" * 60)
    
    try:
        # 导入主程序类
        import importlib.util
        spec = importlib.util.spec_from_file_location("main_module", "Multi-clustering3_LabelCorrection_Modular.py")
        main_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(main_module)
        AISDataProcessor = main_module.AISDataProcessor
        
        # 创建处理器实例
        processor = AISDataProcessor()
        
        # 配置参数
        config = {
            'file_path': r"E:\实验代码备份\ais data\excel_output_长江\切1\筛选结果_经纬度航向.csv",
            'save_dir': r"E:\实验代码备份\project 1 trajectory cluster\pictures\test_centroid_flow",
            'cluster_method': 'dbscan_advanced',
            'partition_count': 15,
            
            # 数据切分模块配置
            'longitude_col_index': 0,
            'latitude_col_index': 1,
            'custom_partition_lengths': {
                0: 2,    # 第1段使用2倍宽度
                -1: 2    # 最后1段使用2倍宽度
            },
            
            # 聚类参数
            'dbscan_advanced': {
                'eps': 0.0008,
                'min_samples': 15,
                'algorithm': 'ball_tree',
                'metric': 'haversine'
            },
            
            # 中心点计算参数
            'centroid_kmeans': {
                'n_clusters': 1,
                'max_iter': 300,
                'n_init': 10,
                'random_state': 42,
                'min_points': 5
            },
            
            # 噪点筛选参数
            'noise_filter': {
                'n_neighbors': 50,
                'min_ratio': 0.5,
            },
        }
        
        # 设置配置
        processor.config = config
        
        print("🔄 步骤1: 数据读取和切分...")
        if not processor.read_and_partition_data():
            print("❌ 数据读取失败")
            return False
        
        print("\n🔄 步骤2: 执行聚类分析...")
        if not processor.perform_clustering():
            print("❌ 聚类分析失败")
            return False
        
        print("\n🔄 步骤3: 噪点筛选...")
        if not processor.filter_noise_points():
            print("❌ 噪点筛选失败")
            return False
        
        print("\n🔄 步骤4: 中心点计算（基于筛选后的数据）...")
        if not processor.compute_centroids_kmeans():
            print("❌ 中心点计算失败")
            return False
        
        print("\n✅ 新流程测试完成!")
        print(f"   - 上游中心点数量: {len(processor.up_centers)}")
        print(f"   - 下游中心点数量: {len(processor.down_centers)}")
        
        # 验证中心点数据
        if processor.up_centers:
            print(f"   - 上游第一个中心点: {processor.up_centers[0]}")
        if processor.down_centers:
            print(f"   - 下游第一个中心点: {processor.down_centers[0]}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_new_centroid_flow()
    if success:
        print("\n🎉 新的中心点计算流程测试通过!")
        print("   现在中心点是基于筛选后的干净数据计算的，避免了重复计算。")
    else:
        print("\n💥 新的中心点计算流程测试失败!")
