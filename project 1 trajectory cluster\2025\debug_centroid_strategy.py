"""
调试中心点策略问题
"""

import sys
import os
import importlib.util

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_centroid_strategy():
    """调试中心点策略"""
    
    print("=" * 60)
    print("🔍 调试中心点策略")
    print("=" * 60)
    
    try:
        # 导入主程序类
        spec = importlib.util.spec_from_file_location("main_module", "Multi-clustering3_LabelCorrection_Modular.py")
        main_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(main_module)
        AISDataProcessor = main_module.AISDataProcessor
        
        # 创建处理器实例
        processor = AISDataProcessor()
        
        # 简化配置，只关注中心点计算
        config = {
            'file_path': r"E:\实验代码备份\ais data\excel_output_长江\切1\筛选结果_经纬度航向.csv",
            'save_dir': r"E:\实验代码备份\project 1 trajectory cluster\pictures\debug_centroid",
            'cluster_method': 'dbscan_advanced',
            'partition_count': 3,  # 只用3个分区便于调试
            
            # 数据切分模块配置
            'longitude_col_index': 0,
            'latitude_col_index': 1,
            'custom_partition_lengths': {
                0: 1,    # 第1段正常宽度
                -1: 1    # 最后1段正常宽度
            },
            
            # 聚类参数
            'dbscan_advanced': {
                'eps': 0.0008,
                'min_samples': 15,
                'algorithm': 'ball_tree',
                'metric': 'haversine'
            },
            
            # 默认中心点计算参数
            'centroid_kmeans': {
                'n_clusters': 1,
                'max_iter': 300,
                'n_init': 10,
                'random_state': 42,
                'min_points': 5
            },
            
            # 🎯 自定义中心点策略
            'custom_centroid_strategy': {
                -1: {                    # 最后一个区域
                    'direction': 'both', # 针对上下游
                    'n_clusters': 2,     # 生成2个中心点
                    'max_iter': 300,
                    'n_init': 10,
                    'random_state': 42,
                    'min_points': 5,
                    'use_real_point': False
                }
            },
            
            # 噪点筛选参数
            'noise_filter': {
                'n_neighbors': 50,
                'min_ratio': 0.5,
            },
        }
        
        # 设置配置
        processor.config = config
        
        print("🔄 步骤1: 数据读取和切分...")
        if not processor.read_and_partition_data():
            print("❌ 数据读取失败")
            return False
        
        print(f"   - 生成了 {len(processor.orig_input)} 个数据段落")
        
        print("\n🔄 步骤2: 执行聚类分析...")
        if not processor.perform_clustering():
            print("❌ 聚类分析失败")
            return False
        
        print("\n🔄 步骤3: 噪点筛选...")
        if not processor.filter_noise_points():
            print("❌ 噪点筛选失败")
            return False
        
        print("\n🔄 步骤4: 中心点计算（查看调试信息）...")
        
        # 手动调用中心点计算来查看调试信息
        if not processor.compute_centroids_kmeans():
            print("❌ 中心点计算失败")
            return False
        
        print("\n📊 最终结果:")
        print(f"   - 上游中心点总数: {len(processor.up_centers)}")
        print(f"   - 下游中心点总数: {len(processor.down_centers)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    debug_centroid_strategy()
