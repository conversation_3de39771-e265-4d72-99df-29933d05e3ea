import pandas as pd
import matplotlib.pyplot as plt
import os
# 设置GUI后端
import matplotlib

matplotlib.use('TkAgg')

# 改进字体设置，只保留SimHei，并增加回退字体
plt.rcParams["font.family"] = ["SimHei", "WenQuanYi Micro Hei", "Heiti TC", "sans-serif"]
plt.rcParams["axes.unicode_minus"] = False  # 解决负号显示问题

# 配置参数
INPUT_FILE = r"E:\实验代码备份\ais data\excel_output_长江\切2\筛选结果_经纬度航向.csv" # 输入CSV文件路径
OUTPUT_IMAGE = r"E:\实验代码备份\ais data\excel_output_长江\船舶经纬度分布散点图.png"  # 输出图像文件名


def plot_coordinates(longitude_col_index=0, latitude_col_index=1):
    """读取CSV文件中的经纬度数据并绘制散点图"""
    try:
        # 1. 读取CSV文件
        print(f"正在读取文件: {INPUT_FILE}")
        df = pd.read_csv(INPUT_FILE)

        # 2. 验证列数
        if len(df.columns) < max(longitude_col_index, latitude_col_index) + 1:
            raise ValueError(
                f"文件列数不足，至少需要{max(longitude_col_index, latitude_col_index) + 1}列，实际只有{len(df.columns)}列")

        # 3. 提取指定列作为经纬度
        longitude = df.iloc[:, longitude_col_index]  # 指定列: 经度
        latitude = df.iloc[:, latitude_col_index]  # 指定列: 纬度

        # 4. 数据预处理：移除无效值
        valid_mask = longitude.notna() & latitude.notna()
        valid_longitude = longitude[valid_mask]
        valid_latitude = latitude[valid_mask]

        print(f"共读取到{len(df)}条记录，其中有效经纬度记录{len(valid_longitude)}条")

        # 5. 绘制散点图
        plt.figure(figsize=(10, 8))
        plt.scatter(valid_longitude, valid_latitude,
                    c='blue', alpha=0.6, s=0.1, label='船舶位置')

        # 6. 设置图表属性
        plt.title('船舶经纬度分布散点图', fontsize=16)
        plt.xlabel('经度', fontsize=12)
        plt.ylabel('纬度', fontsize=12)
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.legend(loc='upper right')
        plt.tight_layout()

        # 7. 确保保存目录存在
        output_dir = os.path.dirname(OUTPUT_IMAGE)
        os.makedirs(output_dir, exist_ok=True)

        # 保存图表
        plt.savefig(OUTPUT_IMAGE, dpi=300, bbox_inches='tight')
        print(f"图表已保存至: {OUTPUT_IMAGE}")

        # 8. 显示图表
        plt.show()

    except Exception as e:
        print(f"处理过程中发生错误: {str(e)}")


if __name__ == "__main__":
    # 可以根据需要修改这里的列索引
    plot_coordinates(longitude_col_index=0, latitude_col_index=1)