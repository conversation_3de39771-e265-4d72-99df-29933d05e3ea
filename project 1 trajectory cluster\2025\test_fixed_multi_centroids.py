"""
测试修复后的多中心点功能
验证最后一个切分区域的上下游都生成2个中心点
"""

import sys
import os
import importlib.util

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_fixed_multi_centroids():
    """测试修复后的多中心点功能"""
    
    print("=" * 80)
    print("🧪 测试修复后的多中心点功能")
    print("   目标：最后一个区域的上下游都生成2个中心点")
    print("=" * 80)
    
    try:
        # 导入主程序类
        spec = importlib.util.spec_from_file_location("main_module", "Multi-clustering3_LabelCorrection_Modular.py")
        main_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(main_module)
        AISDataProcessor = main_module.AISDataProcessor
        
        # 创建处理器实例
        processor = AISDataProcessor()
        
        # 配置参数
        config = {
            'file_path': r"E:\实验代码备份\ais data\excel_output_长江\切1\筛选结果_经纬度航向.csv",
            'save_dir': r"E:\实验代码备份\project 1 trajectory cluster\pictures\test_fixed_multi_centroids",
            'cluster_method': 'dbscan_advanced',
            'partition_count': 4,  # 使用4个分区便于测试
            
            # 数据切分模块配置
            'longitude_col_index': 0,
            'latitude_col_index': 1,
            'custom_partition_lengths': {
                0: 1,    # 第1段正常宽度
                -1: 1    # 最后1段正常宽度
            },
            
            # 聚类参数
            'dbscan_advanced': {
                'eps': 0.0008,
                'min_samples': 15,
                'algorithm': 'ball_tree',
                'metric': 'haversine'
            },
            
            # 默认中心点计算参数
            'centroid_kmeans': {
                'n_clusters': 1,
                'max_iter': 300,
                'n_init': 10,
                'random_state': 42,
                'min_points': 5
            },
            
            # 🎯 自定义中心点策略
            'custom_centroid_strategy': {
                -1: {                    # 最后一个区域
                    'direction': 'both', # 针对上下游
                    'n_clusters': 2,     # 生成2个中心点
                    'max_iter': 300,
                    'n_init': 10,
                    'random_state': 42,
                    'min_points': 5,
                    'use_real_point': False
                }
            },
            
            # 噪点筛选参数
            'noise_filter': {
                'n_neighbors': 50,
                'min_ratio': 0.5,
            },
        }
        
        # 设置配置
        processor.config = config
        
        print("🔄 步骤1: 数据读取和切分...")
        if not processor.read_and_partition_data():
            print("❌ 数据读取失败")
            return False
        
        total_blocks = len(processor.orig_input)
        print(f"   - 生成了 {total_blocks} 个数据段落")
        
        print("\n🔄 步骤2: 执行聚类分析...")
        if not processor.perform_clustering():
            print("❌ 聚类分析失败")
            return False
        
        print("\n🔄 步骤3: 噪点筛选...")
        if not processor.filter_noise_points():
            print("❌ 噪点筛选失败")
            return False
        
        print("\n🔄 步骤4: 中心点计算（查看调试信息）...")
        
        if not processor.compute_centroids_kmeans():
            print("❌ 中心点计算失败")
            return False
        
        print("\n✅ 修复后的多中心点功能测试完成!")
        
        # 验证结果
        print(f"\n📊 中心点计算结果:")
        print(f"   - 总区域数: {total_blocks}")
        print(f"   - 上游中心点总数: {len(processor.up_centers)}")
        print(f"   - 下游中心点总数: {len(processor.down_centers)}")
        
        # 计算预期的中心点数量
        # 前面区域各1个 + 最后区域2个
        expected_up = (total_blocks - 1) + 2
        expected_down = (total_blocks - 1) + 2
        
        print(f"\n🎯 预期中心点数量:")
        print(f"   - 上游预期: {expected_up} 个")
        print(f"   - 下游预期: {expected_down} 个")
        
        # 验证是否符合预期
        up_match = len(processor.up_centers) == expected_up
        down_match = len(processor.down_centers) == expected_down
        
        print(f"\n✅ 验证结果:")
        print(f"   - 上游中心点数量 {'✅ 正确' if up_match else '❌ 错误'} (实际: {len(processor.up_centers)}, 预期: {expected_up})")
        print(f"   - 下游中心点数量 {'✅ 正确' if down_match else '❌ 错误'} (实际: {len(processor.down_centers)}, 预期: {expected_down})")
        
        if up_match and down_match:
            print(f"\n🎉 成功！最后一个区域的上下游都生成了2个中心点")
        else:
            print(f"\n⚠️  结果不符合预期，请检查调试信息")
        
        return up_match and down_match
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_fixed_multi_centroids()
    if success:
        print("\n🎉 修复后的多中心点功能测试通过!")
    else:
        print("\n💥 修复后的多中心点功能测试失败!")
