import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
import shutil
from pathlib import Path
# 配置中文字体，按优先级尝试不同字体
try:
    # Windows系统常用字体
    plt.rcParams["font.family"] = ["Microsoft YaHei", "SimHei", "SimSun", "KaiTi", "FangSong"]
except:
    # 如果上述字体都不可用，使用默认字体
    plt.rcParams["font.family"] = ["DejaVu Sans"]

plt.rcParams["axes.unicode_minus"] = False  # 解决负号显示问题

# 验证字体设置
import matplotlib.font_manager as fm
available_fonts = [f.name for f in fm.fontManager.ttflist]
chinese_fonts = [font for font in ["Microsoft YaHei", "SimHei", "SimSun", "KaiTi"] if font in available_fonts]
if chinese_fonts:
    plt.rcParams["font.family"] = [chinese_fonts[0]]
    print(f"✅ 使用中文字体: {chinese_fonts[0]}")
else:
    print("⚠️  未找到中文字体，使用默认字体（中文可能显示为方块）")
def read_data(file_path, longitude_col_index=0, latitude_col_index=1):
    """
    读取包含经纬度数据的CSV文件。
    使用列索引而不是列名来确保正确读取经纬度数据。

    Args:
        file_path: CSV文件路径
        longitude_col_index: 经度列的索引（默认为0，即第一列）
        latitude_col_index: 纬度列的索引（默认为1，即第二列）
    """
    df = pd.read_csv(file_path)

    # 验证列数
    if len(df.columns) < max(longitude_col_index, latitude_col_index) + 1:
        raise ValueError(
            f"文件列数不足，至少需要{max(longitude_col_index, latitude_col_index) + 1}列，实际只有{len(df.columns)}列")

    # 提取指定列作为经纬度，并重命名列
    longitude = df.iloc[:, longitude_col_index]  # 指定列: 经度
    latitude = df.iloc[:, latitude_col_index]   # 指定列: 纬度

    # 创建新的DataFrame，使用标准列名
    result_df = pd.DataFrame({
        'longitude': longitude,
        'latitude': latitude
    })

    # 如果有第三列（航向），也包含进来
    if len(df.columns) > 2:
        result_df['heading'] = df.iloc[:, 2]

    # 数据预处理：移除无效值
    valid_mask = result_df['longitude'].notna() & result_df['latitude'].notna()
    result_df = result_df[valid_mask].reset_index(drop=True)

    print(f"✅ 数据读取完成，原始记录: {len(df)}，有效记录: {len(result_df)}")
    print(f"📊 经度范围: {result_df['longitude'].min():.6f} ~ {result_df['longitude'].max():.6f}")
    print(f"📊 纬度范围: {result_df['latitude'].min():.6f} ~ {result_df['latitude'].max():.6f}")

    return result_df


def get_bounds(df):
    """
    获取经纬度的最大最小值。
    """
    lat_max = df['latitude'].max()
    lat_min = df['latitude'].min()
    lon_max = df['longitude'].max()
    lon_min = df['longitude'].min()
    return lat_max, lat_min, lon_max, lon_min


def construct_rectangle(lat_max, lat_min, lon_max, lon_min):
    """
    构建矩形区域并计算高度和宽度。
    """
    height = lat_max - lat_min
    width = lon_max - lon_min
    return height, width


def clear_output_directory(output_dir):
    """
    清空输出目录中的所有文件和子目录。
    """
    if os.path.exists(output_dir):
        print(f"🗑️  正在清空输出目录: {output_dir}")
        shutil.rmtree(output_dir)
        print("✅ 输出目录已清空")

    # 重新创建目录
    os.makedirs(output_dir, exist_ok=True)
    print(f"📁 已创建输出目录: {output_dir}")


def determine_start_corner(df, lat_max, lon_min, lon_max, lat_step, lon_step):
    """
    判断从左上角或右上角开始划分区域。
    """
    # 左上角区域
    lat_upper = lat_max
    lat_lower = lat_max - lat_step
    lon_left = lon_min
    lon_right = lon_min + lon_step
    data_left = df[(df['latitude'] <= lat_upper) & (df['latitude'] >= lat_lower) &
                   (df['longitude'] >= lon_left) & (df['longitude'] <= lon_right)]

    # 右上角区域
    lon_left_r = lon_max - lon_step
    lon_right_r = lon_max
    data_right = df[(df['latitude'] <= lat_upper) & (df['latitude'] >= lat_lower) &
                    (df['longitude'] >= lon_left_r) & (df['longitude'] <= lon_right_r)]

    if not data_left.empty:
        return 'left'
    elif not data_right.empty:
        return 'right'
    else:
        return None


def calculate_dynamic_regions(target_regions, custom_region_sizes=None):
    """
    根据自定义区域大小计算动态调整后的区域配置。

    Args:
        target_regions: 目标切分区域数量
        custom_region_sizes: 自定义区域大小字典，格式为 {region_index: size_multiplier}

    Returns:
        tuple: (实际区域配置列表, 实际区域数量)
    """
    if custom_region_sizes is None:
        custom_region_sizes = {}

    # 计算总的宽度单位
    total_width_units = sum(custom_region_sizes.get(i, 1.0) for i in range(1, target_regions + 1))

    # 生成区域配置列表
    region_configs = []
    for i in range(1, target_regions + 1):
        size_multiplier = custom_region_sizes.get(i, 1.0)
        region_configs.append({
            'original_index': i,
            'size_multiplier': size_multiplier,
            'width_units': size_multiplier
        })

    # 计算实际生成的区域数量（去除被合并的区域）
    actual_regions = len([config for config in region_configs if config['size_multiplier'] > 0])

    return region_configs, actual_regions, total_width_units


def divide_and_process(df, lat_max, lat_min, lon_max, lon_min, target_regions, start_corner, custom_region_sizes=None):
    """
    划分矩形区域并在每个区域内读取新数据。
    支持动态调整切分总量的自定义区域大小。

    Args:
        df: 数据DataFrame
        lat_max, lat_min, lon_max, lon_min: 数据边界
        target_regions: 目标切分区域数量
        start_corner: 起始角落
        custom_region_sizes: 自定义区域大小字典，格式为 {region_index: size_multiplier}
                           例如 {1: 2.0, 15: 2.0} 表示第1个和第15个区域为2倍大小
    """
    if custom_region_sizes is None:
        custom_region_sizes = {}

    # 计算动态区域配置
    region_configs, actual_regions, total_width_units = calculate_dynamic_regions(target_regions, custom_region_sizes)

    # 计算基础步长（基于总宽度单位）
    base_lat_step = (lat_max - lat_min) / total_width_units
    base_lon_step = (lon_max - lon_min) / total_width_units

    print(f"🔧 动态切分配置:")
    print(f"   - 目标区域数量: {target_regions}")
    print(f"   - 总宽度单位: {total_width_units:.1f}")
    print(f"   - 基础步长: 纬度 {base_lat_step:.6f}, 经度 {base_lon_step:.6f}")

    if custom_region_sizes:
        print(f"🎯 自定义区域大小:")
        for region_idx, multiplier in custom_region_sizes.items():
            print(f"   - 区域 {region_idx}: {multiplier}倍宽度")

    # 计算每个区域的累积边界
    collected_data = pd.DataFrame()
    data_list = []
    region_info = []

    current_lat_offset = 0
    current_lon_offset = 0

    for config in region_configs:
        region_idx = config['original_index']
        size_multiplier = config['size_multiplier']

        # 计算当前区域的步长
        region_lat_step = base_lat_step * size_multiplier
        region_lon_step = base_lon_step * size_multiplier

        # 更新累积偏移
        current_lat_offset += region_lat_step
        current_lon_offset += region_lon_step

        # 计算区域边界
        if start_corner == 'left':
            lat_upper = lat_max
            lat_lower = lat_max - current_lat_offset
            lon_left = lon_min
            lon_right = lon_min + current_lon_offset
        else:
            lat_upper = lat_max
            lat_lower = lat_max - current_lat_offset
            lon_left = lon_max - current_lon_offset
            lon_right = lon_max

        # 确保边界不超出数据范围
        lat_lower = max(lat_lower, lat_min)
        lon_left = max(lon_left, lon_min)
        lon_right = min(lon_right, lon_max)

        # 获取区域数据
        region_data = df[(df['latitude'] <= lat_upper) & (df['latitude'] >= lat_lower) &
                         (df['longitude'] >= lon_left) & (df['longitude'] <= lon_right)]

        # 去除已收集的数据（差集）
        new_data = region_data[~region_data.index.isin(collected_data.index)]
        collected_data = pd.concat([collected_data, new_data])
        data_list.append(new_data)

        # 记录区域信息
        region_info.append({
            'region': region_idx,
            'size_multiplier': size_multiplier,
            'lat_range': (lat_lower, lat_upper),
            'lon_range': (lon_left, lon_right),
            'data_count': len(new_data),
            'width_units': size_multiplier
        })

    # 打印区域详细信息
    print(f"\n📋 区域划分详情:")
    for info in region_info:
        print(f"   区域 {info['region']}: {info['size_multiplier']}x宽度, "
              f"数据点: {info['data_count']}, "
              f"纬度: {info['lat_range'][0]:.6f}~{info['lat_range'][1]:.6f}, "
              f"经度: {info['lon_range'][0]:.6f}~{info['lon_range'][1]:.6f}")

    # 计算实际生成的有效区域数量
    effective_regions = len([info for info in region_info if info['data_count'] > 0])
    print(f"\n📊 切分结果统计:")
    print(f"   - 目标区域数量: {target_regions}")
    print(f"   - 实际生成区域: {len(region_info)}")
    print(f"   - 有效区域数量: {effective_regions}")
    print(f"   - 总宽度单位消耗: {total_width_units:.1f}")

    return data_list, region_info


def plot_overall_partition_visualization(df, lat_max, lat_min, lon_max, lon_min, n, start_corner, output_dir, data_list, region_info, custom_region_sizes=None):
    """
    生成包含所有原始数据点和切分边界的整体可视化图。
    正确显示每个区域的实际范围（差集部分），支持自定义区域大小。
    """
    plt.figure(figsize=(16, 12))

    # 绘制所有原始数据点
    plt.scatter(df['longitude'], df['latitude'], s=1, c='lightgray', alpha=0.4, label='原始数据点')

    # 为每个区域分配不同颜色
    actual_regions = len(data_list)
    colors = plt.cm.Set3(np.linspace(0, 1, actual_regions))

    # 绘制实际区域的数据点
    for i, (data, info) in enumerate(zip(data_list, region_info)):
        if not data.empty:
            # 根据区域大小调整点的大小
            point_size = 3 + info['size_multiplier']  # 大区域用更大的点

            plt.scatter(data['longitude'], data['latitude'],
                       s=point_size, c=[colors[i]], alpha=0.7,
                       label=f'区域 {info["region"]} ({info["size_multiplier"]}x)')

            # 在区域数据的中心位置添加编号
            center_lon = data['longitude'].mean()
            center_lat = data['latitude'].mean()

            # 根据区域大小调整标签大小
            font_size = 12 + info['size_multiplier'] * 2

            plt.text(center_lon, center_lat, str(info['region']),
                    fontsize=font_size, fontweight='bold',
                    ha='center', va='center',
                    bbox=dict(boxstyle='round,pad=0.4',
                             facecolor='white', alpha=0.9,
                             edgecolor=colors[i], linewidth=2))

    # 绘制累积矩形边界线（用于显示切分策略）
    base_lat_step = (lat_max - lat_min) / n
    base_lon_step = (lon_max - lon_min) / n

    cumulative_lat_step = 0
    cumulative_lon_step = 0

    for i in range(1, len(region_info) + 1):
        # 获取当前区域的大小倍数
        size_multiplier = custom_region_sizes.get(i, 1.0) if custom_region_sizes else 1.0

        cumulative_lat_step += base_lat_step * size_multiplier
        cumulative_lon_step += base_lon_step * size_multiplier

        if start_corner == 'left':
            lat_upper = lat_max
            lat_lower = lat_max - cumulative_lat_step
            lon_left = lon_min
            lon_right = lon_min + cumulative_lon_step
        else:
            lat_upper = lat_max
            lat_lower = lat_max - cumulative_lat_step
            lon_left = lon_max - cumulative_lon_step
            lon_right = lon_max

        # 确保边界不超出数据范围
        lat_lower = max(lat_lower, lat_min)
        lon_left = max(lon_left, lon_min) if start_corner == 'left' else max(lon_left, lon_min)
        lon_right = min(lon_right, lon_max)

        # 绘制矩形边界
        line_style = '-' if size_multiplier != 1.0 else '--'  # 自定义大小的区域用实线
        line_width = 2 if size_multiplier != 1.0 else 1

        rect_x = [lon_left, lon_right, lon_right, lon_left, lon_left]
        rect_y = [lat_lower, lat_lower, lat_upper, lat_upper, lat_lower]
        plt.plot(rect_x, rect_y, color='red' if size_multiplier != 1.0 else 'black',
                linewidth=line_width, alpha=0.6, linestyle=line_style)

    # 设置图形属性
    title = f'数据切分整体可视化 (起始方向: {"左上角" if start_corner == "left" else "右上角"})'
    if custom_region_sizes:
        title += f'\n自定义区域: {custom_region_sizes}'

    plt.title(title, fontsize=16, fontweight='bold')
    plt.xlabel('经度', fontsize=12)
    plt.ylabel('纬度', fontsize=12)
    plt.grid(True, alpha=0.3)

    # 只显示前几个区域的图例，避免图例过于拥挤
    handles, labels = plt.gca().get_legend_handles_labels()
    if len(handles) > 10:
        plt.legend(handles[:10], labels[:10], loc='upper left', bbox_to_anchor=(1.02, 1))
    else:
        plt.legend(loc='upper left', bbox_to_anchor=(1.02, 1))

    # 设置坐标轴范围，留出一些边距
    lon_margin = (lon_max - lon_min) * 0.05
    lat_margin = (lat_max - lat_min) * 0.05
    plt.xlim(lon_min - lon_margin, lon_max + lon_margin)
    plt.ylim(lat_min - lat_margin, lat_max + lat_margin)

    # 保存整体可视化图
    overall_image_path = os.path.join(output_dir, 'overall_partition_visualization.png')
    plt.savefig(overall_image_path, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"📊 整体切分可视化图已保存至: {overall_image_path}")


def plot_data(data_list, output_dir):
    """
    为每个区域绘制数据分布图，并保存对应的CSV文件。
    """
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    for idx, data in enumerate(data_list, start=1):
        if data.empty:
            print(f"⚠️  区域 {idx} 没有数据，跳过")
            continue

        # 绘制并保存图像
        plt.figure(figsize=(8, 6))
        plt.scatter(data['longitude'], data['latitude'], s=10, c='blue', alpha=0.5)
        plt.title(f'区域 {idx} 的数据分布 (共 {len(data)} 个点)')
        plt.xlabel('经度')
        plt.ylabel('纬度')
        plt.grid(True)
        image_path = os.path.join(output_dir, f'region_{idx}.png')
        plt.savefig(image_path)
        plt.close()

        # 保存CSV文件
        csv_path = os.path.join(output_dir, f'data{idx}.csv')
        data.to_csv(csv_path, index=False)

        print(f"✅ 区域 {idx}: 保存了 {len(data)} 个数据点")


def main():
    print("=" * 60)
    print("🚀 数据切分程序启动 - 动态切分总量版本")
    print("=" * 60)

    # 设置参数
    file_path = r"E:\实验代码备份\ais data\excel_output_长江\切2\筛选结果_经纬度航向.csv"
    target_regions = 15  # 目标切分区域数量
    output_dir = r"E:\实验代码备份\ais data\excel_output_长江\切2\切分"

    # 🔧 自定义区域宽度配置
    # 格式: {区域编号: 宽度倍数}
    # 系统会根据自定义宽度动态调整总切分数量
    custom_region_sizes = {
        1: 2.0,   # 第一个区域设为2倍宽度，扩大首段覆盖范围
        15: 2.0   # 最后一个区域设为2倍宽度，增强末段鲁棒性
    }

    print(f"📂 输入文件: {file_path}")
    print(f"🎯 目标切分区域数量: {target_regions}")
    print(f"📁 输出目录: {output_dir}")

    # 计算动态调整后的配置
    if custom_region_sizes:
        print(f"\n🔧 自定义区域宽度配置:")
        for region_idx, multiplier in custom_region_sizes.items():
            print(f"   - 区域 {region_idx}: {multiplier}倍宽度")

        # 计算总宽度单位和实际区域数量
        total_width_units = sum(custom_region_sizes.get(i, 1.0) for i in range(1, target_regions + 1))
        standard_regions = target_regions - len(custom_region_sizes)
        custom_width_units = sum(custom_region_sizes.values()) - len(custom_region_sizes)

        print(f"\n📊 动态调整计算:")
        print(f"   - 标准区域数量: {standard_regions} (1倍宽度)")
        print(f"   - 自定义区域数量: {len(custom_region_sizes)}")
        print(f"   - 额外宽度单位: {custom_width_units:.1f}")
        print(f"   - 总宽度单位: {total_width_units:.1f}")
        print(f"   - 实际生成区域: {target_regions} (宽度单位总和: {total_width_units:.1f})")

    print("-" * 60)

    # 清空输出目录
    clear_output_directory(output_dir)
    print("-" * 60)

    # 读取数据
    print("📖 正在读取数据...")
    df = read_data(file_path, longitude_col_index=0, latitude_col_index=1)
    print("-" * 60)

    # 获取边界和矩形尺寸
    lat_max, lat_min, lon_max, lon_min = get_bounds(df)
    height, width = construct_rectangle(lat_max, lat_min, lon_max, lon_min)

    print(f"🗺️  数据边界信息:")
    print(f"   纬度范围: {lat_min:.6f} ~ {lat_max:.6f} (高度: {height:.6f})")
    print(f"   经度范围: {lon_min:.6f} ~ {lon_max:.6f} (宽度: {width:.6f})")
    print("-" * 60)

    # 判断起始角（使用临时步长）
    temp_lat_step = height / target_regions
    temp_lon_step = width / target_regions
    start_corner = determine_start_corner(df, lat_max, lon_min, lon_max, temp_lat_step, temp_lon_step)
    if start_corner is None:
        print("❌ 无法在左上角或右上角找到数据，尝试扩大区域或检查数据。")
        return

    # 明确提示起始方向
    start_direction = "左上角" if start_corner == "left" else "右上角"
    print(f"🎯 切分起始方向: {start_direction}")
    print(f"   切分策略: 从{start_direction}开始，根据自定义宽度动态调整区域大小")
    print("-" * 60)

    # 划分区域并处理数据
    print("🔄 正在进行动态区域划分...")
    data_list, region_info = divide_and_process(df, lat_max, lat_min, lon_max, lon_min, target_regions, start_corner, custom_region_sizes)
    print("✅ 区域划分完成")
    print("-" * 60)

    # 生成整体可视化图
    print("🎨 正在生成整体切分可视化图...")
    plot_overall_partition_visualization(df, lat_max, lat_min, lon_max, lon_min, target_regions, start_corner, output_dir, data_list, region_info, custom_region_sizes)
    print("-" * 60)

    # 绘制各区域图并保存数据
    print("📊 正在生成各区域图像和保存数据...")
    plot_data(data_list, output_dir)
    print("-" * 60)

    # 详细统计信息
    total_points = sum(len(data) for data in data_list)
    non_empty_regions = sum(1 for data in data_list if not data.empty)
    actual_regions = len(data_list)

    print("📈 最终切分统计:")
    print(f"   总数据点数: {len(df)}")
    print(f"   已分配点数: {total_points}")
    print(f"   目标区域数: {target_regions}")
    print(f"   实际区域数: {actual_regions}")
    print(f"   有效区域数: {non_empty_regions}")
    print(f"   数据覆盖率: {total_points/len(df)*100:.1f}%")

    # 显示自定义区域的效果对比
    if custom_region_sizes:
        print(f"\n🎯 自定义宽度区域效果:")
        for region_idx, multiplier in custom_region_sizes.items():
            if region_idx <= len(data_list):
                data_count = len(data_list[region_idx - 1])
                print(f"   - 区域 {region_idx} ({multiplier}x宽度): {data_count} 个数据点")

        # 计算标准区域的平均数据量作为对比
        standard_regions_data = [len(data_list[i-1]) for i in range(1, len(data_list)+1)
                               if i not in custom_region_sizes and len(data_list[i-1]) > 0]
        if standard_regions_data:
            avg_standard = sum(standard_regions_data) / len(standard_regions_data)
            print(f"   - 标准区域平均: {avg_standard:.0f} 个数据点")

    print("-" * 60)

    print(f"🎉 动态切分完成！所有结果已保存至: {output_dir}")
    print("   生成的文件包括:")
    print("   - overall_partition_visualization.png (整体切分可视化)")
    print("   - region_X.png (各区域散点图)")
    print("   - dataX.csv (各区域数据文件)")
    print("=" * 60)


if __name__ == "__main__":
    main()
