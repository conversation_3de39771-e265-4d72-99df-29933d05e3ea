"""
测试自定义中心点策略功能
验证最后一个区域的蓝色散点使用真实原始数据点作为中心点
"""

import sys
import os
import importlib.util

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_custom_centroid_strategy():
    """测试自定义中心点策略"""
    
    print("=" * 70)
    print("🧪 测试自定义中心点策略")
    print("   目标：最后一个区域的蓝色散点使用真实原始数据点")
    print("=" * 70)
    
    try:
        # 导入主程序类
        spec = importlib.util.spec_from_file_location("main_module", "Multi-clustering3_LabelCorrection_Modular.py")
        main_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(main_module)
        AISDataProcessor = main_module.AISDataProcessor
        
        # 创建处理器实例
        processor = AISDataProcessor()
        
        # 配置参数（包含自定义中心点策略）
        config = {
            'file_path': r"E:\实验代码备份\ais data\excel_output_长江\切1\筛选结果_经纬度航向.csv",
            'save_dir': r"E:\实验代码备份\project 1 trajectory cluster\pictures\test_custom_centroid",
            'cluster_method': 'dbscan_advanced',
            'partition_count': 10,  # 使用较少的分区便于测试
            
            # 数据切分模块配置
            'longitude_col_index': 0,
            'latitude_col_index': 1,
            'custom_partition_lengths': {
                0: 2,    # 第1段使用2倍宽度
                -1: 2    # 最后1段使用2倍宽度
            },
            
            # 聚类参数
            'dbscan_advanced': {
                'eps': 0.0008,
                'min_samples': 15,
                'algorithm': 'ball_tree',
                'metric': 'haversine'
            },
            
            # 中心点计算参数
            'centroid_kmeans': {
                'n_clusters': 1,
                'max_iter': 300,
                'n_init': 10,
                'random_state': 42,
                'min_points': 5
            },
            
            # 🎯 自定义中心点策略 - 关键配置！
            'custom_centroid_strategy': {
                -1: {                    # 最后一个区域
                    'direction': 'down', # 针对下游（蓝色散点）
                    'use_real_point': True  # 使用真实原始数据点
                }
            },
            
            # 噪点筛选参数
            'noise_filter': {
                'n_neighbors': 50,
                'min_ratio': 0.5,
            },
        }
        
        # 设置配置
        processor.config = config
        
        print("🔄 步骤1: 数据读取和切分...")
        if not processor.read_and_partition_data():
            print("❌ 数据读取失败")
            return False
        
        print(f"   - 生成了 {len(processor.orig_input)} 个数据段落")
        
        print("\n🔄 步骤2: 执行聚类分析...")
        if not processor.perform_clustering():
            print("❌ 聚类分析失败")
            return False
        
        print("\n🔄 步骤3: 噪点筛选...")
        if not processor.filter_noise_points():
            print("❌ 噪点筛选失败")
            return False
        
        print("\n🔄 步骤4: 中心点计算（应用自定义策略）...")
        if not processor.compute_centroids_kmeans():
            print("❌ 中心点计算失败")
            return False
        
        print("\n✅ 自定义中心点策略测试完成!")
        
        # 验证结果
        total_blocks = len(processor.up_centers)
        print(f"\n📊 中心点计算结果:")
        print(f"   - 总区域数: {total_blocks}")
        print(f"   - 上游中心点数量: {len(processor.up_centers)}")
        print(f"   - 下游中心点数量: {len(processor.down_centers)}")
        
        if processor.down_centers:
            last_down_center = processor.down_centers[-1]
            print(f"\n🎯 最后一个区域的下游中心点（应为真实数据点）:")
            print(f"   - 经度: {last_down_center[0]:.6f}")
            print(f"   - 纬度: {last_down_center[1]:.6f}")
            print(f"   - 航向: {last_down_center[2]:.2f}°")
            
            # 检查是否为真实数据点
            last_down_data = processor.down_data[-1]
            if len(last_down_data) > 0:
                # 检查中心点是否存在于原始数据中
                matches = []
                for i, point in enumerate(last_down_data):
                    if (abs(point[0] - last_down_center[0]) < 1e-10 and 
                        abs(point[1] - last_down_center[1]) < 1e-10 and 
                        abs(point[2] - last_down_center[2]) < 1e-10):
                        matches.append(i)
                
                if matches:
                    print(f"   ✅ 确认：中心点是真实原始数据点（索引: {matches[0]}）")
                else:
                    print(f"   ⚠️  警告：中心点可能不是完全匹配的原始数据点")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_custom_centroid_strategy()
    if success:
        print("\n🎉 自定义中心点策略测试通过!")
        print("   最后一个区域的蓝色散点现在使用真实原始数据点作为中心点。")
        print("   其他区域保持原有的虚拟中心点计算逻辑。")
    else:
        print("\n💥 自定义中心点策略测试失败!")
