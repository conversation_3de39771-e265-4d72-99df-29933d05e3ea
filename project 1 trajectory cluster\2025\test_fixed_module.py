"""
测试修复后的数据切分模块2
"""

from 数据切分模块2 import partition_data

def test_fixed_module():
    """测试修复后的数据切分模块"""
    
    print("=" * 60)
    print("🧪 测试修复后的数据切分模块2")
    print("=" * 60)
    
    # 测试参数
    file_path = r"E:\实验代码备份\ais data\excel_output_长江\切2\筛选结果_经纬度航向.csv"
    target_segments = 15
    
    # 自定义段落宽度配置
    custom_lengths = {
        0: 2,    # 第1段使用2倍宽度
        -1: 2    # 最后1段使用2倍宽度
    }
    
    try:
        print("🔄 测试修复后的切分功能...")
        
        # 调用修复后的数据切分模块
        data_list = partition_data(
            file_path=file_path,
            target_segments=target_segments,
            custom_lengths=custom_lengths,
            longitude_col_index=0,
            latitude_col_index=1
        )
        
        print(f"\n📊 修复后的切分结果:")
        print(f"   - 生成段落数量: {len(data_list)}")
        
        total_points = 0
        non_empty_segments = 0
        empty_segments = []
        
        for i, data in enumerate(data_list):
            if not data.empty:
                non_empty_segments += 1
                total_points += len(data)
                print(f"   - 段落 {i+1}: {len(data)} 个数据点")
            else:
                empty_segments.append(i+1)
                print(f"   - 段落 {i+1}: 空段落")
        
        print(f"\n✅ 修复效果验证:")
        print(f"   - 有效段落数: {non_empty_segments}/{len(data_list)}")
        print(f"   - 空段落数: {len(empty_segments)}")
        if empty_segments:
            print(f"   - 空段落编号: {empty_segments}")
        print(f"   - 总数据点数: {total_points}")
        
        # 与原始数据对比
        import pandas as pd
        original_df = pd.read_csv(file_path)
        print(f"   - 原始数据点数: {len(original_df)}")
        print(f"   - 数据覆盖率: {total_points/len(original_df)*100:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_fixed_module()
    if success:
        print("\n🎉 修复后的数据切分模块2测试通过!")
    else:
        print("\n💥 修复后的数据切分模块2测试失败!")
